import React from 'react'
import { Modal, Form } from 'antd'
import { updateModel } from '@/api/model'
import ModelForm from './ModelForm'
import { useLanguage } from '@/locales/LanguageContext'
import { useNotification } from "@/hooks/useNotification.ts";

interface ModelEditModalProps {
  open: boolean
  onCancel: () => void
  providerId: string
  groups: Set<string>
  onSuccess?: () => void
  initialValues: {
    id: string
    modelId: string
    name: string
    type: string
    features?: string[]
    group?: string
  }
}


const ModelEditModal: React.FC<ModelEditModalProps> = ({
  open,
  onCancel,
  providerId,
  groups,
  onSuccess,
  initialValues,
}) => {
  const { t } = useLanguage()
  const [form] = Form.useForm()
  const {notify, contextHolder} = useNotification()

  React.useEffect(() => {
    if (open) {
      form.setFieldsValue(initialValues)
    } else {
      form.resetFields()
    }
  }, [open, initialValues, form])

  React.useEffect(() => {
    return () => {
      form.resetFields()
    }
  }, [])

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields()
      await updateModel(
        { model_id: initialValues.id },
        {
          provider_id: providerId,
          model_id: values.modelId,
          name: values.name || values.modelId,
          group: values.group || 'Default',
          type: values.type,
          features: values.features,
        }
      )
      notify.success('编辑模型成功')
      onCancel()
      onSuccess?.()
    } catch (error) {
      console.error('编辑模型失败:', error)
      notify.error('编辑模型失败')
    }
  }

  return (
    <>
      {contextHolder}
      <Modal
        title={t('model_service.model.update_title')}
        open={open}
        onCancel={onCancel}
        onOk={handleSubmit}
        okText="保存"
        cancelText="取消"
      >
        <ModelForm form={form} groups={groups} initialValues={initialValues} />
      </Modal>
    </>
  )
}

export default ModelEditModal
