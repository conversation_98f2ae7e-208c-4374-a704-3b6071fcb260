import { useLanguage } from "@/locales/LanguageContext"
import { Model } from '@/types/model'
import ModelListCard from "./ModelListCard"
import { Button, Input, Tooltip } from "antd"
import { PlusOutlined, SettingOutlined } from "@ant-design/icons";
import React from "react";

// 模型列表区域组件
interface ModelListSectionProps {
  provider: API.ProviderResponse
  models: Model[]
  searchKeyword: string
  onAddModel: () => void
  onManageModels: () => void
  onEditModel: (model: Model) => void
  onDeleteModel: (model: Model) => void
  onSearchChange: (keyword: string) => void
}

const ModelListSection: React.FC<ModelListSectionProps> = ({
                                                             provider,
                                                             models,
                                                             searchKeyword,
                                                             onAddModel,
                                                             onManageModels,
                                                             onEditModel,
                                                             onDeleteModel,
                                                             onSearchChange,
                                                           }) => {
  const {t} = useLanguage()

  // 过滤模型
  const filteredModels = models.filter(model =>
    model.id.toLowerCase().includes(searchKeyword.toLowerCase()) ||
    model.modelName.toLowerCase().includes(searchKeyword.toLowerCase())
  )

  // 分组模型
  const groupedModels = filteredModels.reduce<Record<string, Model[]>>((acc, model) => {
    const provider = model.group || 'Default'
    if (!acc[provider]) acc[provider] = []
    model.added = true
    acc[provider].push(model)
    return acc
  }, {})

  const ModelManageButton = () => {

    const tooltipTitle = !provider.config?.support_models_endpoint ?
      '当前提供商不支持通过接口获取支持的模型列表' : provider.config?.api_key_required && !provider.api_key ? '请先配置 API 密钥' : null

    const canManageModels = provider?.config?.support_models_endpoint && (!provider.config?.api_key_required || provider.api_key)

    return <Tooltip
      title={tooltipTitle}
    >
      <Button
        type="primary"
        icon={<SettingOutlined/>}
        disabled={!canManageModels}
        onClick={onManageModels}
      >
        {t('model_service.model.manage')}
      </Button>
    </Tooltip>
  }

  return (
    <div className="flex flex-col">
      <div className="flex mb-5">
        {/* 新增模型 */}
        <Button icon={<PlusOutlined/>} className="mr-2.5" onClick={onAddModel}>
          {t('model_service.model.add')}
        </Button>
        {/* 获取模型列表 */}
        <ModelManageButton/>
        {/* 搜索模型 */}
        <Input.Search
          placeholder={t('model_service.model.search.placeholder')}
          allowClear
          className="ml-4 w-[300px]"
          onChange={(e) => onSearchChange(e.target.value)}
          value={searchKeyword}
        />
      </div>
      <div className="flex text-xs text-gray-500 mb-4">
        查看
        {' '}
        <a
          href={provider?.config?.help_doc_url}
          target="_blank"
          rel="noopener noreferrer"
        >
          {provider.name}文档
        </a>
        {' '}
        和
        {' '}
        <a
          href={provider?.config?.help_doc_url}
          target="_blank"
          rel="noopener noreferrer"
        >
          模型
        </a>
        {' '}
        获取更多详情
      </div>
      {/* 模型列表 */}
      <div className="flex flex-col gap-4">
        {Object.entries(groupedModels).map(([ provider, models ]) => (
          <ModelListCard
            key={provider}
            group={provider}
            provider={provider}
            models={models}
            onEditModel={onEditModel}
            onDeleteModel={onDeleteModel}
          />
        ))}
      </div>
    </div>
  )
}

export default ModelListSection