import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { Flex, Input, Modal, Radio } from 'antd'
import axios from 'axios'

import {
  isAudioModel,
  isEmbeddingModel,
  isImageModel,
  isReasoningModel,
  isRerankModel,
  isVideoModel,
  isVisionModel
} from '@/utils/model.tsx'
import { addModel, deleteModel } from '@/api/model.ts'
import ModelListCard from '@/pages/Settings/ModelService/components/ModelListCard.tsx'
import { Model } from '@/types/model'
import { useNotification } from "@/hooks/useNotification.ts";

const {Search} = Input

// 常量定义
const DEFAULT_MODEL_TYPES = [
  'all',
  'reasoning',
  'vision',
  'video',
  'audio',
  'music',
  'embedding',
  'reranker',
] as const

const PROVIDER_FORMATTERS = {
  GITHUB: 'github',
  FIREWORKS: 'fireworksai',
} as const

interface ModelManageProps {
  open: boolean
  onCancel: () => void
  providerId: string
  name: string
  apiKey: string
  baseUrl: string
  addedModelIdMapping: Record<string, string> // key 为 modelId，value 为 id
  onSuccess?: () => void
}

/**
 * 根据模型名称确定分组
 */
const determineGroupByModelName = (modelName: string): string => {
  const modelNameParts = modelName.split('-')

  if (modelNameParts.length >= 2) {
    const versionRegex = /-(\d+(\.\d+)?[bB]?)$/
    const capitalizeFirst = (str: string) => str.charAt(0).toUpperCase() + str.slice(1)

    if (versionRegex.test(`-${modelNameParts[1]}`)) {
      return capitalizeFirst(modelNameParts[0])
    } else {
      return `${capitalizeFirst(modelNameParts[0])}-${modelNameParts[1]}`
    }
  }

  return modelNameParts[0].charAt(0).toUpperCase() + modelNameParts[0].slice(1)
}

/**
 * 确定模型名称和分组
 */
const determineModelNameAndGroup = (model: Model): void => {
  if (model.modelId.includes('/')) {
    // 例如：THUDM/glm-4-9b-chat，group 为 THUDM，name 为 glm-4-9b-chat
    const [ providerPart, namePart ] = model.modelId.split('/')
    model.group = providerPart.charAt(0).toUpperCase() + providerPart.slice(1)
    model.modelName = namePart
  } else if (model.modelId.includes('-')) {
    // 例如：glm-4-9b-chat，则 name 为 glm-4-9b-chat
    model.modelName = model.modelId
    model.group = determineGroupByModelName(model.modelName)
  } else {
    model.group = 'Default'
    model.modelName = model.modelId
  }
}


/**
 * 确定模型类型和特性
 */
const determineModelTypeAndFeature = (model: Model): void => {
  if (!model) {
    return
  }

  // 确定模型类型
  if (isVideoModel(model)) {
    model.type = 'video'
  } else if (isAudioModel(model)) {
    model.type = 'audio'
  } else if (isRerankModel(model)) {
    model.type = 'reranker'
  } else if (isEmbeddingModel(model)) {
    model.type = 'embedding'
  } else if (isImageModel(model)) {
    model.type = 'image'
  } else {
    model.type = 'llm'
  }

  // 确定模型特性
  const features: string[] = []
  if (isVisionModel(model)) {
    features.push('vision')
  }
  if (isReasoningModel(model)) {
    features.push('reasoning')
  }
  model.features = features

  // 生成标签
  const tags: string[] = [ model.type ]
  if (model.features?.length > 0) {
    tags.push(...model.features)
  }
  model.tags = tags
}

const ModelManage: React.FC<ModelManageProps> = ({
                                                   open,
                                                   onCancel,
                                                   providerId,
                                                   name,
                                                   apiKey,
                                                   baseUrl,
                                                   addedModelIdMapping,
                                                   onSuccess,
                                                 }) => {
  const {notify, contextHolder} = useNotification()
  const [ models, setModels ] = useState<Model[]>([])
  const [ loading, setLoading ] = useState(false)
  const [ searchText, setSearchText ] = useState('')
  const [ selectedType, setSelectedType ] = useState('all')
  const [ modelTypes, setModelTypes ] = useState<string[]>([ ...DEFAULT_MODEL_TYPES ])

  /**
   * 通用模型格式化函数
   */
  const formatModelsCommon = useCallback((formattedModels: Model[]) => {
    const tags = new Set<string>()

    formattedModels.forEach(model => {
      model.added = model.modelId in addedModelIdMapping
      model.tags.forEach(tag => tags.add(tag))
    })

    tags.delete('llm') // 移除默认的 llm 标签
    setModels(formattedModels)
    setModelTypes([ 'all', ...Array.from(tags) ])
  }, [ addedModelIdMapping ])

  /**
   * 格式化默认模型
   */
  const formatDefaultModels = useCallback((rawModels: any[]) => {
    const formattedModels: Model[] = rawModels.map((model: any) => {
      const newModel: Model = {...model, modelId: model.id}
      determineModelNameAndGroup(newModel)
      determineModelTypeAndFeature(newModel)
      return newModel
    })
    formatModelsCommon(formattedModels)
  }, [ formatModelsCommon ])

  /**
   * 格式化 GitHub 模型
   */
  const formatGithubModels = useCallback((rawModels: any[]) => {
    const formattedModels: Model[] = rawModels.map((model: any) => {
      const newModel: Model = {...model, modelId: model.id}
      newModel.modelName = model?.name
      newModel.group = model?.model_family?.charAt(0).toUpperCase() + model?.model_family?.slice(1)
      determineModelTypeAndFeature(newModel)
      return newModel
    })
    formatModelsCommon(formattedModels)
  }, [ formatModelsCommon ])

  /**
   * 格式化 Fireworks 模型
   */
  const formatFireworksModels = useCallback((rawModels: any[]) => {
    const formattedModels: Model[] = rawModels.map((model: any) => {
      const newModel: Model = {...model, modelId: model.id}
      newModel.modelName = model.id.split('/').pop()
      newModel.group = determineGroupByModelName(newModel.modelName)
      determineModelTypeAndFeature(newModel)
      return newModel
    })
    formatModelsCommon(formattedModels)
  }, [ formatModelsCommon ])

  /**
   * 获取模型列表
   */
  const fetchModels = useCallback(async () => {
    if (!open || !baseUrl) return

    setLoading(true)
    try {
      const url = baseUrl.replace(/\/$/, '')
      const response = await axios.get(`${url}/models`, {
        headers: {
          Authorization: `Bearer ${apiKey}`,
        },
      })

      if (response.data) {
        const rawModels = response.data?.data ? response.data.data : response.data
        console.log(`模型提供商：${providerId}，提供如下模型：`, rawModels)

        // 根据不同提供商格式化模型
        switch (providerId) {
          case PROVIDER_FORMATTERS.GITHUB:
            formatGithubModels(rawModels)
            break
          case PROVIDER_FORMATTERS.FIREWORKS:
            formatFireworksModels(rawModels)
            break
          default:
            formatDefaultModels(rawModels)
            break
        }
      }
    } catch (error) {
      notify.error('获取模型列表失败')
      console.error('获取模型列表失败:', error)
    } finally {
      setLoading(false)
    }
  }, [ open, apiKey, baseUrl, providerId])

  // 打开时获取模型列表
  useEffect(() => {
    fetchModels()
  }, [ fetchModels ])

  // 关闭时清空模型列表
  useEffect(() => {
    if (!open) {
      setModels([])
      setSearchText('')
      setSelectedType('all')
    }
  }, [ open ])

  // 根据类型及关键词过滤模型列表
  const filteredModels = useMemo(() => {
    return models.filter(model => {
      const matchesSearch =
        model.id.toLowerCase().includes(searchText.toLowerCase()) ||
        model.modelName.toLowerCase().includes(searchText.toLowerCase())
      const matchesType = selectedType === 'all' || model.tags.includes(selectedType)
      return matchesSearch && matchesType
    })
  }, [ models, searchText, selectedType ])

  // 根据提供商分组模型
  const groupedModels = useMemo(() => {
    return filteredModels.reduce<Record<string, Model[]>>((acc, model) => {
      const group = model.group || 'Default'
      if (!acc[group]) acc[group] = []
      acc[group].push(model)
      return acc
    }, {})
  }, [ filteredModels ])

  /**
   * 添加模型
   */
  const handleAddModel = useCallback(async (model: Model) => {
    try {
      await addModel({
        provider_id: providerId,
        model_id: model.modelId,
        name: model.modelName,
        type: model.type,
        group: model.group,
        features: model.features,
      })

      notify.success(`添加模型 ${model.modelName} 成功`)

      // 更新模型状态
      setModels(prevModels =>
        prevModels.map(m =>
          m.modelId === model.modelId ? {...m, added: true} : m
        )
      )

      onSuccess?.()
    } catch (error) {
      console.error('添加模型失败:', error)
      notify.error(`添加模型 ${model.modelName} 失败`)
    }
  }, [ providerId, notify, onSuccess ])

  /**
   * 删除模型
   */
  const handleDeleteModel = useCallback(async (model: Model) => {
    try {
      await deleteModel({
        model_id: addedModelIdMapping[model.modelId],
      })

      notify.success(`删除模型 ${model.modelName} 成功`)

      // 更新模型状态
      setModels(prevModels =>
        prevModels.map(m =>
          m.modelId === model.modelId ? {...m, added: false} : m
        )
      )

      onSuccess?.()
    } catch (error) {
      console.error('删除模型失败:', error)
      notify.error(`删除模型 ${model.modelName} 失败`)
    }
  }, [ addedModelIdMapping, notify, onSuccess ])

  return (
    <>
      {contextHolder}
      <Modal
        title={`${name} 模型列表`}
        open={open}
        onCancel={onCancel}
        footer={null}
        width={800}
        loading={loading}
      >
        <Flex vertical align="center" style={{marginTop: '25px'}}>
          {/* 模型类型快捷筛选 */}
          <Radio.Group
            value={selectedType}
            buttonStyle="solid"
            onChange={e => setSelectedType(e.target.value)}
          >
            {modelTypes.map(type => (
              <Radio.Button key={type} value={type}>
                {type.charAt(0).toUpperCase() + type.slice(1)}
              </Radio.Button>
            ))}
          </Radio.Group>

          {/* 搜索框 */}
          <Search
            placeholder="Search model id or name"
            allowClear
            style={{margin: '15px 0'}}
            onChange={e => setSearchText(e.target.value)}
            value={searchText}
          />
        </Flex>

        {/* 模型列表 */}
        <Flex vertical style={{height: '600px', overflowY: 'auto'}} gap={16}>
          {Object.entries(groupedModels).map(([ group, models ]) => (
            <ModelListCard
              key={group}
              group={group}
              provider={providerId}
              models={models}
              onAddModel={handleAddModel}
              onDeleteModel={handleDeleteModel}
            />
          ))}
        </Flex>
      </Modal>
    </>
  )
}

export default ModelManage
