import React, { useEffect } from 'react'
import { Modal, Form } from 'antd'
import { addModel } from '@/api/model'
import ModelForm from './ModelForm'
import { useLanguage } from '@/locales/LanguageContext'
import { useNotification } from "@/hooks/useNotification.ts";

interface ModelAddModalProps {
  open: boolean
  onCancel: () => void
  providerId: string
  groups: Set<string>
  onSuccess?: () => void
}

const ModelAddModal: React.FC<ModelAddModalProps> = ({
  open,
  onCancel,
  providerId,
  groups,
  onSuccess,
}) => {
  const { t } = useLanguage()
  const [form] = Form.useForm()
  const {notify, contextHolder} = useNotification()

  useEffect(() => {
    if (open) {
      form.resetFields()
    }
  }, [open, form])

  useEffect(() => {
    return () => {
      form.resetFields()
    }
  }, [])

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields()
      await addModel({
        provider_id: providerId,
        model_id: values.modelId,
        name: values.name || values.modelId,
        group: values.group || 'Default',
        type: values.type,
        features: values.features,
      })
      notify.success('添加模型成功')
      onCancel()
      onSuccess?.()
    } catch (error) {
      console.error('添加模型失败:', error)
      notify.error('添加模型失败')
    }
  }

  return (
    <>
      {contextHolder}
      <Modal
        title={t('model_service.model.add_title')}
        open={open}
        onCancel={onCancel}
        onOk={handleSubmit}
        okText="确认"
        cancelText="取消"
      >
        <ModelForm form={form} groups={groups} />
      </Modal>
    </>
  )
}

export default ModelAddModal
